<?xml version="1.0" encoding="UTF-8"?>
<!-- 全局属性示例Schema -->
<schema top_node="document" 
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://pricing-dev.oss-cn-hangzhou.aliyuncs.com/moduforge/moduforge-schema.xsd">
  <!-- 全局属性定义 -->
  <global_attributes>
    <!-- 适用于所有块级元素的通用属性 -->
    <global_attribute types="document section paragraph heading list">
      <attr name="id"/>
      <attr name="class"/>
      <attr name="style"/>
      <attr name="data-component"/>
    </global_attribute>
    
    <!-- 适用于所有元素的通用属性 -->
    <global_attribute types="*">
      <attr name="data-custom"/>
      <attr name="data-version" default="1.0"/>
      <attr name="data-created"/>
    </global_attribute>
    
    <!-- 适用于文本相关元素的属性 -->
    <global_attribute types="paragraph heading text">
      <attr name="lang" default="zh-CN"/>
      <attr name="dir" default="ltr"/>
    </global_attribute>
    
    <!-- 适用于交互元素的属性 -->
    <global_attribute types="link button">
      <attr name="role"/>
      <attr name="aria-label"/>
      <attr name="tabindex" default="0"/>
    </global_attribute>
  </global_attributes>

  <!-- 节点定义 -->
  <nodes>
    <!-- 文档根节点 -->
    <node name="document" group="block">
      <desc>文档根节点</desc>
      <content>section+</content>
      <attrs>
        <attr name="title" default="New Document"/>
        <attr name="author"/>
        <attr name="created_date"/>
      </attrs>
    </node>
    
    <!-- 章节节点 -->
    <node name="section" group="block">
      <desc>文档章节</desc>
      <content>(heading paragraph | list)+</content>
      <attrs>
        <attr name="level" default="1"/>
        <attr name="collapsed" default="false"/>
      </attrs>
    </node>
    
    <!-- 段落节点 -->
    <node name="paragraph" group="block">
      <desc>段落节点</desc>
      <content>inline*</content>
      <marks>strong em link code</marks>
      <attrs>
        <attr name="align" default="left"/>
        <attr name="indent" default="0"/>
      </attrs>
    </node>
    
    <!-- 标题节点 -->
    <node name="heading" group="block">
      <desc>标题节点</desc>
      <content>inline*</content>
      <marks>strong em</marks>
      <attrs>
        <attr name="level" default="1"/>
        <attr name="anchor"/>
      </attrs>
    </node>
    
    <!-- 列表节点 -->
    <node name="list" group="block">
      <desc>列表节点</desc>
      <content>listitem+</content>
      <attrs>
        <attr name="type" default="bullet"/>
        <attr name="start" default="1"/>
        <attr name="tight" default="true"/>
      </attrs>
    </node>
    
    <!-- 列表项节点 -->
    <node name="listitem">
      <desc>列表项节点</desc>
      <content>paragraph</content>
    </node>
    
    <!-- 文本节点 -->
    <node name="text">
      <desc>文本节点，叶子节点</desc>
    </node>
    
    <!-- 按钮节点 -->
    <node name="button" group="inline">
      <desc>按钮节点</desc>
      <content>text*</content>
      <attrs>
        <attr name="type" default="button"/>
        <attr name="disabled" default="false"/>
        <attr name="onclick"/>
      </attrs>
    </node>
  </nodes>
  
  <!-- 标记定义 -->
  <marks>
    <!-- 粗体标记 -->
    <mark name="strong" group="formatting">
      <desc>粗体文本标记</desc>
      <spanning>true</spanning>
    </mark>
    
    <!-- 斜体标记 -->
    <mark name="em" group="formatting">
      <desc>斜体文本标记</desc>
      <spanning>true</spanning>
    </mark>
    
    <!-- 链接标记 -->
    <mark name="link" group="link">
      <desc>超链接标记</desc>
      <spanning>false</spanning>
      <attrs>
        <attr name="href"/>
        <attr name="title"/>
        <attr name="target" default="_self"/>
      </attrs>
    </mark>
    
    <!-- 代码标记 -->
    <mark name="code" group="formatting">
      <desc>内联代码标记</desc>
      <spanning>true</spanning>
      <attrs>
        <attr name="language"/>
        <attr name="highlight" default="false"/>
      </attrs>
    </mark>
  </marks>
</schema>
