[package]
name = "moduforge-macros-derive"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 派生宏定义"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}

[lib]
name = "mf_derive"
proc-macro = true

[dependencies]
syn = { version = "2.0", features = ["full"] }
quote = "1.0"
proc-macro2 = "1.0"

# DI宏所需依赖
anyhow = {workspace=true}
moduforge-model = {version = "0.4.12", path = "../model"}
moduforge-state = {version = "0.4.12", path = "../state"}
moduforge-transform = {version = "0.4.12", path = "../transform"}
moduforge-core = {version = "0.4.12", path = "../core"} 