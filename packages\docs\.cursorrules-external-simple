# ModuForge-RS 集成项目 - 简化规则
# 专为使用 ModuForge-RS 库的项目定制

## 核心库信息
ModuForge-RS 是基于 Rust 的状态管理和数据转换框架，采用：
- **不可变数据结构** (im-rs)
- **事件驱动架构**
- **插件系统**
- **事务式状态管理**

## 快速集成

### 依赖配置
```toml
[dependencies]
moduforge-core = "0.4.11"
moduforge-model = "0.4.11"
moduforge-state = "0.4.11"
moduforge-transform = "0.4.11"
moduforge-rules-engine = "0.4.11"
moduforge-rules-expression = "0.4.11"
moduforge-collaboration = "0.4.11"
moduforge-template = "0.4.11"

# 必需的支持库
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
im = { version = "15.1", features = ["serde"] }
anyhow = "1"
async-trait = "0.1"
```

### 基本使用模式
```rust
// 导入核心模块
use mf_core::{ForgeResult, async_runtime::AsyncRuntime, types::RuntimeOptions};
use mf_state::{init_logging, StateConfig};

// 初始化
async fn setup() -> ForgeResult<AsyncRuntime> {
    init_logging("info", None)?;
    let options = RuntimeOptions::default();
    let state_config = StateConfig::default();
    AsyncRuntime::new(options, state_config).await
}
```

## 代码约定

### 数据结构
- 使用 `im::HashMap`, `im::Vector` 代替标准集合
- 优先 `Arc<T>` 进行异步共享
- 使用 `ForgeResult<T>` 处理框架错误

### 错误处理
```rust
use mf_core::ForgeResult;
use anyhow::Result;

// 框架操作用 ForgeResult
fn framework_op() -> ForgeResult<()> { Ok(()) }

// 业务逻辑用 anyhow::Result  
fn business_op() -> Result<()> { Ok(()) }
```

### 插件开发
```rust
use mf_core::extension::Extension;
use mf_state::{
    plugin::{Plugin, PluginSpec, PluginTrait, StateField},
    resource::Resource
};

// 创建插件状态
#[derive(Debug, Clone)]
struct MyState { count: u64 }
impl Resource for MyState {}

// 创建插件
let plugin = Plugin::new(PluginSpec {
    key: ("my_plugin".to_string(), "v1".to_string()),
    state_field: Some(Arc::new(MyStateField)),
    tr: Some(Arc::new(MyPlugin)),
    priority: 10,
});

// 添加到扩展
let mut extension = Extension::new();
extension.add_plugin(Arc::new(plugin));
```

## 常用导入
```rust
// 核心运行时和扩展
use mf_core::{
    ForgeResult, async_runtime::AsyncRuntime, 
    types::{RuntimeOptions, Extensions},
    extension::Extension
};

// 插件系统
use mf_state::{
    transaction::Transaction, 
    plugin::{Plugin, PluginSpec, PluginTrait, StateField},
    resource::Resource,
    init_logging
};

// 数据模型
use mf_model::{node::Node, mark::Mark, attrs::Attrs};

// 转换操作
use mf_transform::{
    node_step::{AddNodeStep, RemoveNodeStep},
    attr_step::AttrStep,
    mark_step::{AddMarkStep, RemoveMarkStep}
};

// 不可变集合和异步
use im::{HashMap as ImHashMap, Vector as ImVector};
use tokio::sync::{Arc, Mutex};
use async_trait::async_trait;
```

## 性能要点
- 优先借用 (`&T`) 而非克隆
- 使用 `Arc<T>` 共享数据
- 避免在异步代码中阻塞
- 最小化内存分配

## 测试模板
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use mf_core::{async_runtime::AsyncRuntime, types::RuntimeOptions};
    use mf_state::StateConfig;

    #[tokio::test]
    async fn test_integration() {
        let options = RuntimeOptions::default();
        let state_config = StateConfig::default();
        let runtime = AsyncRuntime::new(options, state_config).await.unwrap();
        // 测试逻辑
    }
}
```

## AI 助手提示
在帮助此项目时请：
1. 使用 ModuForge-RS 的不可变数据模式
2. 建议事件驱动解决方案
3. 遵循插件架构模式
4. 优先类型安全和异步最佳实践
5. 使用适当的错误处理 (`ForgeResult` vs `anyhow::Result`) 