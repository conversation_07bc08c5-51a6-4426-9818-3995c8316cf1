{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "sourceMap": true, "typeRoots": ["node_modules/@types", "./types.d.ts"]}, "include": ["**/*.ts", "**/*.vue", "types.d.ts"], "exclude": ["node_modules", "dist", "cache"]}