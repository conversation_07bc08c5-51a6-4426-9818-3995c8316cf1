/**
 * Customize default theme styling by overriding CSS variables:
 * https://github.com/vuejs/vitepress/blob/main/src/client/theme-default/styles/vars.css
 */

:root {
  --vp-c-brand-1: #FF6B35;
  --vp-c-brand-2: #F7931E;
  --vp-c-brand-3: #FF6B35;
  --vp-c-brand-soft: rgba(255, 107, 53, 0.14);
}

.dark {
  --vp-c-brand-1: #FF6B35;
  --vp-c-brand-2: #F7931E;
  --vp-c-brand-3: #FF6B35;
  --vp-c-brand-soft: rgba(255, 107, 53, 0.16);
}

/**
 * Component: Button
 * -------------------------------------------------------------------------- */

:root {
  --vp-button-brand-border: transparent;
  --vp-button-brand-text: var(--vp-c-white);
  --vp-button-brand-bg: var(--vp-c-brand-3);
  --vp-button-brand-hover-border: transparent;
  --vp-button-brand-hover-text: var(--vp-c-white);
  --vp-button-brand-hover-bg: var(--vp-c-brand-2);
  --vp-button-brand-active-border: transparent;
  --vp-button-brand-active-text: var(--vp-c-white);
  --vp-button-brand-active-bg: var(--vp-c-brand-1);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(
    120deg,
    #FF6B35 30%,
    #F7931E
  );

  --vp-home-hero-image-background-image: linear-gradient(
    -45deg,
    #FF6B35 50%,
    #F7931E 50%
  );
  --vp-home-hero-image-filter: blur(44px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(68px);
  }
}

/**
 * Component: Custom Block
 * -------------------------------------------------------------------------- */

:root {
  --vp-custom-block-tip-border: transparent;
  --vp-custom-block-tip-text: var(--vp-c-text-1);
  --vp-custom-block-tip-bg: var(--vp-c-brand-soft);
  --vp-custom-block-tip-code-bg: var(--vp-c-brand-soft);
}

/**
 * Component: Algolia
 * -------------------------------------------------------------------------- */

.DocSearch {
  --docsearch-primary-color: var(--vp-c-brand-1) !important;
}

/**
 * Custom styles for better code highlighting
 * -------------------------------------------------------------------------- */

.vp-code-group .tabs input:checked + label {
  background-color: var(--vp-c-brand-soft);
  border-bottom-color: var(--vp-c-brand-1);
}

/**
 * Custom spacing and typography
 * -------------------------------------------------------------------------- */

.VPContent .vp-doc h1,
.VPContent .vp-doc h2,
.VPContent .vp-doc h3 {
  margin-top: 2rem;
}

.VPContent .vp-doc h1:first-child {
  margin-top: 0;
}

/* 代码块样式优化 */
.vp-code-group {
  margin: 16px 0;
}

div[class*='language-'] {
  margin: 16px 0;
  border-radius: 8px;
}

/* 侧边栏样式优化 */
.VPSidebar {
  --vp-sidebar-width: 280px;
}

.VPSidebarItem .text {
  font-weight: 500;
}

.VPSidebarItem.level-0 .text {
  font-weight: 600;
}

/* 响应式优化 */
@media (max-width: 960px) {
  .VPContent {
    padding: 32px 24px 96px;
  }
}

/* 特色区块样式 */
.vp-feature {
  border: 1px solid var(--vp-c-bg-soft);
  border-radius: 12px;
  transition: border-color 0.25s, box-shadow 0.25s;
}

.vp-feature:hover {
  border-color: var(--vp-c-brand-1);
  box-shadow: 0 4px 16px rgba(255, 107, 53, 0.1);
} 