[package]
name = "moduforge-macros"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 宏定义"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}
[lib]
name = "mf_macro"
path="./src/lib.rs"

[dependencies]
moduforge-model = {version = "0.4.12", path = "../model"}
moduforge-state = {version = "0.4.12", path = "../state"}
moduforge-transform = {version = "0.4.12", path = "../transform"}
moduforge-core = {version = "0.4.12", path = "../core"}
anyhow = {workspace=true}
async-trait = {workspace=true}

