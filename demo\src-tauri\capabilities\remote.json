{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "remote-capability", "description": "enables permissions for remote URLs (localhost development)", "windows": ["*"], "webviews": ["*"], "remote": {"urls": ["http://localhost:*", "https://localhost:*"]}, "local": true, "permissions": ["core:default", "core:window:default", "core:window:allow-create", "core:window:allow-start-dragging", "core:window:allow-close", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-toggle-maximize", "core:window:allow-unmaximize", "core:window:allow-show", "core:window:allow-hide", "core:window:allow-set-focus", "core:window:allow-center", "core:window:allow-set-position", "core:window:allow-set-size", "core:window:allow-is-maximized", "core:window:allow-is-minimized", "core:window:allow-is-visible", "core:window:allow-is-focused", "core:webview:default", "core:webview:allow-create-webview-window"]}