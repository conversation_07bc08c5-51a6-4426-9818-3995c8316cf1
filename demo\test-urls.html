<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        .url-display {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 微前端模块 URL 测试</h1>
    
    <div class="test-section info">
        <h3>📋 测试说明</h3>
        <p>此页面用于测试微前端模块的 URL 生成逻辑，验证开发环境和生产环境的 URL 是否正确。</p>
    </div>

    <div class="test-section">
        <h3>🌍 环境检测</h3>
        <div id="env-info"></div>
    </div>

    <div class="test-section">
        <h3>🔗 URL 生成测试</h3>
        <div id="url-tests"></div>
    </div>

    <div class="test-section">
        <h3>🌐 可访问性测试</h3>
        <button onclick="testUrls()">测试所有 URL</button>
        <div id="accessibility-results"></div>
    </div>

    <script>
        // 模拟模块配置
        const modules = [
            {
                key: 'rough-estimate',
                title: '概算',
                port: 5174
            },
            {
                key: 'main-shell',
                title: '主应用',
                port: 5173
            }
        ];

        // 检测环境
        function detectEnvironment() {
            const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            const port = window.location.port;
            
            return {
                isDev,
                protocol,
                hostname,
                port,
                fullUrl: window.location.href
            };
        }

        // 生成模块 URL
        function generateModuleUrl(module, env) {
            if (env.isDev) {
                return `http://localhost:${module.port}`;
            } else {
                return `${module.key}/index.html`;
            }
        }

        // 测试 URL 可访问性
        async function testUrlAccessibility(url) {
            try {
                const response = await fetch(url, { method: 'HEAD' });
                return {
                    success: response.ok,
                    status: response.status,
                    statusText: response.statusText
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // 显示环境信息
        function displayEnvironmentInfo() {
            const env = detectEnvironment();
            const envInfo = document.getElementById('env-info');
            
            envInfo.innerHTML = `
                <div class="url-display">
                    <strong>当前环境:</strong> ${env.isDev ? '开发环境' : '生产环境'}<br>
                    <strong>协议:</strong> ${env.protocol}<br>
                    <strong>主机:</strong> ${env.hostname}<br>
                    <strong>端口:</strong> ${env.port || '默认'}<br>
                    <strong>完整 URL:</strong> ${env.fullUrl}
                </div>
            `;
        }

        // 显示 URL 测试结果
        function displayUrlTests() {
            const env = detectEnvironment();
            const urlTests = document.getElementById('url-tests');
            
            let html = '';
            modules.forEach(module => {
                const url = generateModuleUrl(module, env);
                html += `
                    <div class="url-display">
                        <strong>${module.title} (${module.key}):</strong><br>
                        生成的 URL: <code>${url}</code><br>
                        <small>端口: ${module.port}</small>
                    </div>
                `;
            });
            
            urlTests.innerHTML = html;
        }

        // 测试所有 URL
        async function testUrls() {
            const env = detectEnvironment();
            const resultsDiv = document.getElementById('accessibility-results');
            resultsDiv.innerHTML = '<p>🔄 正在测试...</p>';
            
            let html = '';
            for (const module of modules) {
                const url = generateModuleUrl(module, env);
                const result = await testUrlAccessibility(url);
                
                const statusClass = result.success ? 'success' : 'error';
                const statusText = result.success 
                    ? `✅ 可访问 (${result.status} ${result.statusText})`
                    : `❌ 不可访问 (${result.error || result.status + ' ' + result.statusText})`;
                
                html += `
                    <div class="test-section ${statusClass}">
                        <strong>${module.title}:</strong> ${statusText}<br>
                        <small>URL: ${url}</small>
                    </div>
                `;
            }
            
            resultsDiv.innerHTML = html;
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', function() {
            displayEnvironmentInfo();
            displayUrlTests();
        });
    </script>
</body>
</html>
