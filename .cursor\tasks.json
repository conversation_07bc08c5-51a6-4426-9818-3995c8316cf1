{"version": "2.0.0", "tasks": [{"label": "cargo build", "type": "cargo", "command": "build", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$rustc"]}, {"label": "cargo check", "type": "cargo", "command": "check", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": ["$rustc"]}, {"label": "cargo test", "type": "cargo", "command": "test", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$rustc"]}, {"label": "cargo clippy", "type": "shell", "command": "cargo", "args": ["clippy", "--all-targets", "--all-features"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$rustc"]}, {"label": "cargo fmt", "type": "shell", "command": "cargo", "args": ["fmt"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"label": "cargo run", "type": "cargo", "command": "run", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$rustc"]}, {"label": "cargo clean", "type": "cargo", "command": "clean", "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}}, {"label": "cargo doc", "type": "shell", "command": "cargo", "args": ["doc", "--open", "--no-deps"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "Run Full Check", "dependsOrder": "sequence", "dependsOn": ["cargo fmt", "cargo clippy", "cargo test"], "group": {"kind": "test", "isDefault": true}}]}