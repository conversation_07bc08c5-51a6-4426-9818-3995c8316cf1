{"nodes": [{"id": "115975ef-2f43-4e22-b553-0da6f4cc7f68", "type": "inputNode", "position": {"x": 180, "y": 240}, "name": "Request"}, {"id": "138b3b11-ff46-450f-9704-3f3c712067b2", "type": "functionNode", "position": {"x": 470, "y": 240}, "name": "functionNode 1", "content": "/**\n* @param {import('gorules').Input} input\n* @param {{\n*  moment: import('dayjs')\n*  env: Record<string, any>\n* }} helpers\n*/\nconst handler = (input, { moment, env }) => {\n  return {\n    output: input.input * 2,\n  };\n}"}, {"id": "db8797b1-bcc1-4fbf-a5d8-e7d43a181d5e", "type": "outputNode", "position": {"x": 780, "y": 240}, "name": "Response"}], "edges": [{"id": "05740fa7-3755-4756-b85e-bc1af2f6773b", "sourceId": "115975ef-2f43-4e22-b553-0da6f4cc7f68", "type": "edge", "targetId": "138b3b11-ff46-450f-9704-3f3c712067b2"}, {"id": "5d89c1d6-e894-4e8a-bd13-22368c2a6bc7", "sourceId": "138b3b11-ff46-450f-9704-3f3c712067b2", "type": "edge", "targetId": "db8797b1-bcc1-4fbf-a5d8-e7d43a181d5e"}]}