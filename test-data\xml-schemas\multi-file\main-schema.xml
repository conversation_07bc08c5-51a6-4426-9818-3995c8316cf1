<?xml version="1.0" encoding="UTF-8"?>
<!-- 主Schema文件，引用其他文件 -->
<schema top_node="doc">
  <!-- 导入基础节点和标记 -->
  <imports>
    <import src="./base-nodes.xml"/>
    <import src="./formatting-marks.xml"/>
    <import src="./link-marks.xml"/>
  </imports>
  
  <!-- 包含表格扩展 -->
  <includes>
    <include src="./table-extension.xml"/>
  </includes>
  
  <!-- 本文件特有的节点定义 -->
  <nodes>
    <!-- 列表节点 -->
    <node name="list" group="block">
      <desc>列表节点</desc>
      <content>listitem+</content>
      <attrs>
        <attr name="type" default="bullet"/>
        <attr name="start" default="1"/>
      </attrs>
    </node>
    
    <!-- 列表项节点 -->
    <node name="listitem">
      <desc>列表项节点</desc>
      <content>paragraph</content>
    </node>
    
    <!-- 代码块节点 -->
    <node name="codeblock" group="block">
      <desc>代码块节点</desc>
      <content>text*</content>
      <attrs>
        <attr name="language"/>
        <attr name="line_numbers" default="false"/>
      </attrs>
    </node>
  </nodes>
  
  <!-- 本文件特有的标记定义 -->
  <marks>
    <!-- 高亮标记 -->
    <mark name="highlight" group="formatting">
      <desc>高亮文本标记</desc>
      <spanning>true</spanning>
      <attrs>
        <attr name="color" default="yellow"/>
      </attrs>
    </mark>
  </marks>
</schema>
