import { existsSync, readdirSync } from 'fs';
import { join } from 'path';

console.log('🔍 检查构建产物...');

const distPath = './dist';
const expectedDirs = ['main-shell', 'rough-estimate'];
const expectedFiles = ['index.html', 'splashscreen.html', 'favicon.ico'];

// 检查主要文件
console.log('\n📁 检查主要文件:');
expectedFiles.forEach(file => {
  const filePath = join(distPath, file);
  if (existsSync(filePath)) {
    console.log(`✅ ${file} - 存在`);
  } else {
    console.log(`❌ ${file} - 缺失`);
  }
});

// 检查子模块目录
console.log('\n📁 检查子模块目录:');
expectedDirs.forEach(dir => {
  const dirPath = join(distPath, dir);
  if (existsSync(dirPath)) {
    console.log(`✅ ${dir}/ - 存在`);
    
    // 检查子模块的 index.html
    const indexPath = join(dirPath, 'index.html');
    if (existsSync(indexPath)) {
      console.log(`  ✅ ${dir}/index.html - 存在`);
    } else {
      console.log(`  ❌ ${dir}/index.html - 缺失`);
    }
    
    // 检查 assets 目录
    const assetsPath = join(dirPath, 'assets');
    if (existsSync(assetsPath)) {
      const assetFiles = readdirSync(assetsPath);
      console.log(`  ✅ ${dir}/assets/ - 存在 (${assetFiles.length} 个文件)`);
    } else {
      console.log(`  ❌ ${dir}/assets/ - 缺失`);
    }
  } else {
    console.log(`❌ ${dir}/ - 缺失`);
  }
});

// 检查主应用的 assets
console.log('\n📁 检查主应用 assets:');
const mainAssetsPath = join(distPath, 'assets');
if (existsSync(mainAssetsPath)) {
  const assetFiles = readdirSync(mainAssetsPath);
  console.log(`✅ assets/ - 存在 (${assetFiles.length} 个文件)`);
  assetFiles.forEach(file => {
    console.log(`  - ${file}`);
  });
} else {
  console.log(`❌ assets/ - 缺失`);
}

console.log('\n🎉 构建产物检查完成！');
