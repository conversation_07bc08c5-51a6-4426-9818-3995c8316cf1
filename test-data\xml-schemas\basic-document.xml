<?xml version="1.0" encoding="UTF-8"?>
<!-- 基础文档Schema定义 -->
<schema top_node="doc" 
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://pricing-dev.oss-cn-hangzhou.aliyuncs.com/moduforge/moduforge-schema.xsd">
  
  <!-- 全局属性定义 -->
  <global_attributes>
    <global_attribute types="paragraph heading">
      <attr name="id"/>
      <attr name="class"/>
      <attr name="style"/>
    </global_attribute>
    <global_attribute types="*">
      <attr name="data-custom" default="default-value"/>
      <attr name="data-source"/>
    </global_attribute>
  </global_attributes>
  
  <nodes>
    <!-- 文档根节点 -->
    <node name="doc" group="block">
      <desc>文档根节点，包含所有内容</desc>
      <content>paragraph+</content>
      <marks>_</marks>
      <attrs>
        <attr name="title" default="Untitled Document"/>
        <attr name="version" default="1.0"/>
        <attr name="created_at"/>
      </attrs>
    </node>
    
    <!-- 段落节点 -->
    <node name="paragraph" group="block">
      <desc>段落节点，包含内联内容</desc>
      <content>inline*</content>
      <marks>strong em link</marks>
      <attrs>
        <attr name="align" default="left"/>
        <attr name="indent" default="0"/>
      </attrs>
    </node>
    
    <!-- 标题节点 -->
    <node name="heading" group="block">
      <desc>标题节点</desc>
      <content>inline*</content>
      <marks>strong em</marks>
      <attrs>
        <attr name="level" default="1"/>
        <attr name="id"/>
      </attrs>
    </node>
    
    <!-- 列表节点 -->
    <node name="list" group="block">
      <desc>列表节点</desc>
      <content>listitem+</content>
      <attrs>
        <attr name="type" default="bullet"/>
        <attr name="start" default="1"/>
      </attrs>
    </node>
    
    <!-- 列表项节点 -->
    <node name="listitem">
      <desc>列表项节点</desc>
      <content>paragraph</content>
    </node>
    
    <!-- 文本节点 -->
    <node name="text">
      <desc>文本节点，叶子节点</desc>
    </node>
  </nodes>
  
  <marks>
    <!-- 粗体标记 -->
    <mark name="strong" group="formatting">
      <desc>粗体文本标记</desc>
      <spanning>true</spanning>
      <attrs>
        <attr name="weight" default="bold"/>
      </attrs>
    </mark>
    
    <!-- 斜体标记 -->
    <mark name="em" group="formatting">
      <desc>斜体文本标记</desc>
      <spanning>true</spanning>
    </mark>
    
    <!-- 链接标记 -->
    <mark name="link" group="link">
      <desc>超链接标记</desc>
      <spanning>false</spanning>
      <attrs>
        <attr name="href"/>
        <attr name="title"/>
        <attr name="target" default="_self"/>
      </attrs>
    </mark>
    
    <!-- 代码标记 -->
    <mark name="code" group="formatting">
      <desc>内联代码标记</desc>
      <spanning>true</spanning>
      <attrs>
        <attr name="language"/>
      </attrs>
    </mark>
  </marks>
</schema>
