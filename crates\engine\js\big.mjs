/**
 * Bundled by jsDelivr using Rollup v2.79.1 and <PERSON>rser v5.19.2.
 * Original file: /npm/big.js@6.2.1/big.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
var r,e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t={exports:{}};r=t,function(e){var t,n=1e6,i=1e6,o="[big.js] ",s=o+"Invalid ",c=s+"decimal places",f=s+"rounding mode",u=o+"Division by zero",h={},l=void 0,a=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;function p(r,e,t,n){var i=r.c;if(t===l&&(t=r.constructor.RM),0!==t&&1!==t&&2!==t&&3!==t)throw Error(f);if(e<1)n=3===t&&(n||!!i[0])||0===e&&(1===t&&i[0]>=5||2===t&&(i[0]>5||5===i[0]&&(n||i[1]!==l))),i.length=1,n?(r.e=r.e-e+1,i[0]=1):i[0]=r.e=0;else if(e<i.length){if(n=1===t&&i[e]>=5||2===t&&(i[e]>5||5===i[e]&&(n||i[e+1]!==l||1&i[e-1]))||3===t&&(n||!!i[0]),i.length=e,n)for(;++i[--e]>9;)if(i[e]=0,0===e){++r.e,i.unshift(1);break}for(e=i.length;!i[--e];)i.pop()}return r}function w(r,e,t){var n=r.e,i=r.c.join(""),o=i.length;if(e)i=i.charAt(0)+(o>1?"."+i.slice(1):"")+(n<0?"e":"e+")+n;else if(n<0){for(;++n;)i="0"+i;i="0."+i}else if(n>0)if(++n>o)for(n-=o;n--;)i+="0";else n<o&&(i=i.slice(0,n)+"."+i.slice(n));else o>1&&(i=i.charAt(0)+"."+i.slice(1));return r.s<0&&t?"-"+i:i}h.abs=function(){var r=new this.constructor(this);return r.s=1,r},h.cmp=function(r){var e,t=this,n=t.c,i=(r=new t.constructor(r)).c,o=t.s,s=r.s,c=t.e,f=r.e;if(!n[0]||!i[0])return n[0]?o:i[0]?-s:0;if(o!=s)return o;if(e=o<0,c!=f)return c>f^e?1:-1;for(s=(c=n.length)<(f=i.length)?c:f,o=-1;++o<s;)if(n[o]!=i[o])return n[o]>i[o]^e?1:-1;return c==f?0:c>f^e?1:-1},h.div=function(r){var e=this,t=e.constructor,i=e.c,o=(r=new t(r)).c,s=e.s==r.s?1:-1,f=t.DP;if(f!==~~f||f<0||f>n)throw Error(c);if(!o[0])throw Error(u);if(!i[0])return r.s=s,r.c=[r.e=0],r;var h,a,w,g,v,d=o.slice(),E=h=o.length,m=i.length,b=i.slice(0,h),P=b.length,x=r,y=x.c=[],D=0,M=f+(x.e=e.e-r.e)+1;for(x.s=s,s=M<0?0:M,d.unshift(0);P++<h;)b.push(0);do{for(w=0;w<10;w++){if(h!=(P=b.length))g=h>P?1:-1;else for(v=-1,g=0;++v<h;)if(o[v]!=b[v]){g=o[v]>b[v]?1:-1;break}if(!(g<0))break;for(a=P==h?o:d;P;){if(b[--P]<a[P]){for(v=P;v&&!b[--v];)b[v]=9;--b[v],b[P]+=10}b[P]-=a[P]}for(;!b[0];)b.shift()}y[D++]=g?w:++w,b[0]&&g?b[P]=i[E]||0:b=[i[E]]}while((E++<m||b[0]!==l)&&s--);return y[0]||1==D||(y.shift(),x.e--,M--),D>M&&p(x,M,t.RM,b[0]!==l),x},h.eq=function(r){return 0===this.cmp(r)},h.gt=function(r){return this.cmp(r)>0},h.gte=function(r){return this.cmp(r)>-1},h.lt=function(r){return this.cmp(r)<0},h.lte=function(r){return this.cmp(r)<1},h.minus=h.sub=function(r){var e,t,n,i,o=this,s=o.constructor,c=o.s,f=(r=new s(r)).s;if(c!=f)return r.s=-f,o.plus(r);var u=o.c.slice(),h=o.e,l=r.c,a=r.e;if(!u[0]||!l[0])return l[0]?r.s=-f:u[0]?r=new s(o):r.s=1,r;if(c=h-a){for((i=c<0)?(c=-c,n=u):(a=h,n=l),n.reverse(),f=c;f--;)n.push(0);n.reverse()}else for(t=((i=u.length<l.length)?u:l).length,c=f=0;f<t;f++)if(u[f]!=l[f]){i=u[f]<l[f];break}if(i&&(n=u,u=l,l=n,r.s=-r.s),(f=(t=l.length)-(e=u.length))>0)for(;f--;)u[e++]=0;for(f=e;t>c;){if(u[--t]<l[t]){for(e=t;e&&!u[--e];)u[e]=9;--u[e],u[t]+=10}u[t]-=l[t]}for(;0===u[--f];)u.pop();for(;0===u[0];)u.shift(),--a;return u[0]||(r.s=1,u=[a=0]),r.c=u,r.e=a,r},h.mod=function(r){var e,t=this,n=t.constructor,i=t.s,o=(r=new n(r)).s;if(!r.c[0])throw Error(u);return t.s=r.s=1,e=1==r.cmp(t),t.s=i,r.s=o,e?new n(t):(i=n.DP,o=n.RM,n.DP=n.RM=0,t=t.div(r),n.DP=i,n.RM=o,this.minus(t.times(r)))},h.neg=function(){var r=new this.constructor(this);return r.s=-r.s,r},h.plus=h.add=function(r){var e,t,n,i=this,o=i.constructor;if(r=new o(r),i.s!=r.s)return r.s=-r.s,i.minus(r);var s=i.e,c=i.c,f=r.e,u=r.c;if(!c[0]||!u[0])return u[0]||(c[0]?r=new o(i):r.s=i.s),r;if(c=c.slice(),e=s-f){for(e>0?(f=s,n=u):(e=-e,n=c),n.reverse();e--;)n.push(0);n.reverse()}for(c.length-u.length<0&&(n=u,u=c,c=n),e=u.length,t=0;e;c[e]%=10)t=(c[--e]=c[e]+u[e]+t)/10|0;for(t&&(c.unshift(t),++f),e=c.length;0===c[--e];)c.pop();return r.c=c,r.e=f,r},h.pow=function(r){var e=this,t=new e.constructor("1"),n=t,o=r<0;if(r!==~~r||r<-1e6||r>i)throw Error(s+"exponent");for(o&&(r=-r);1&r&&(n=n.times(e)),r>>=1;)e=e.times(e);return o?t.div(n):n},h.prec=function(r,e){if(r!==~~r||r<1||r>n)throw Error(s+"precision");return p(new this.constructor(this),r,e)},h.round=function(r,e){if(r===l)r=0;else if(r!==~~r||r<-n||r>n)throw Error(c);return p(new this.constructor(this),r+this.e+1,e)},h.sqrt=function(){var r,e,t,n=this,i=n.constructor,s=n.s,c=n.e,f=new i("0.5");if(!n.c[0])return new i(n);if(s<0)throw Error(o+"No square root");0===(s=Math.sqrt(n+""))||s===1/0?((e=n.c.join("")).length+c&1||(e+="0"),c=((c+1)/2|0)-(c<0||1&c),r=new i(((s=Math.sqrt(e))==1/0?"5e":(s=s.toExponential()).slice(0,s.indexOf("e")+1))+c)):r=new i(s+""),c=r.e+(i.DP+=4);do{t=r,r=f.times(t.plus(n.div(t)))}while(t.c.slice(0,c).join("")!==r.c.slice(0,c).join(""));return p(r,(i.DP-=4)+r.e+1,i.RM)},h.times=h.mul=function(r){var e,t=this,n=t.constructor,i=t.c,o=(r=new n(r)).c,s=i.length,c=o.length,f=t.e,u=r.e;if(r.s=t.s==r.s?1:-1,!i[0]||!o[0])return r.c=[r.e=0],r;for(r.e=f+u,s<c&&(e=i,i=o,o=e,u=s,s=c,c=u),e=new Array(u=s+c);u--;)e[u]=0;for(f=c;f--;){for(c=0,u=s+f;u>f;)c=e[u]+o[f]*i[u-f-1]+c,e[u--]=c%10,c=c/10|0;e[u]=c}for(c?++r.e:e.shift(),f=e.length;!e[--f];)e.pop();return r.c=e,r},h.toExponential=function(r,e){var t=this,i=t.c[0];if(r!==l){if(r!==~~r||r<0||r>n)throw Error(c);for(t=p(new t.constructor(t),++r,e);t.c.length<r;)t.c.push(0)}return w(t,!0,!!i)},h.toFixed=function(r,e){var t=this,i=t.c[0];if(r!==l){if(r!==~~r||r<0||r>n)throw Error(c);for(r=r+(t=p(new t.constructor(t),r+t.e+1,e)).e+1;t.c.length<r;)t.c.push(0)}return w(t,!1,!!i)},h.toJSON=h.toString=function(){var r=this,e=r.constructor;return w(r,r.e<=e.NE||r.e>=e.PE,!!r.c[0])},h.toNumber=function(){var r=Number(w(this,!0,!0));if(!0===this.constructor.strict&&!this.eq(r.toString()))throw Error(o+"Imprecise conversion");return r},h.toPrecision=function(r,e){var t=this,i=t.constructor,o=t.c[0];if(r!==l){if(r!==~~r||r<1||r>n)throw Error(s+"precision");for(t=p(new i(t),r,e);t.c.length<r;)t.c.push(0)}return w(t,r<=t.e||t.e<=i.NE||t.e>=i.PE,!!o)},h.valueOf=function(){var r=this,e=r.constructor;if(!0===e.strict)throw Error(o+"valueOf disallowed");return w(r,r.e<=e.NE||r.e>=e.PE,!0)},t=function r(){function e(t){var n=this;if(!(n instanceof e))return t===l?r():new e(t);if(t instanceof e)n.s=t.s,n.e=t.e,n.c=t.c.slice();else{if("string"!=typeof t){if(!0===e.strict&&"bigint"!=typeof t)throw TypeError(s+"value");t=0===t&&1/t<0?"-0":String(t)}!function(r,e){var t,n,i;if(!a.test(e))throw Error(s+"number");for(r.s="-"==e.charAt(0)?(e=e.slice(1),-1):1,(t=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(t<0&&(t=n),t+=+e.slice(n+1),e=e.substring(0,n)):t<0&&(t=e.length),i=e.length,n=0;n<i&&"0"==e.charAt(n);)++n;if(n==i)r.c=[r.e=0];else{for(;i>0&&"0"==e.charAt(--i););for(r.e=t-n-1,r.c=[],t=0;n<=i;)r.c[t++]=+e.charAt(n++)}}(n,t)}n.constructor=e}return e.prototype=h,e.DP=20,e.RM=1,e.NE=-7,e.PE=21,e.strict=!1,e.roundDown=0,e.roundHalfUp=1,e.roundHalfEven=2,e.roundUp=3,e}(),t.default=t.Big=t,r.exports?r.exports=t:e.Big=t}(e);var n=t.exports;export{n as default};
//# sourceMappingURL=/sm/d5d645c26c6ce952f28329f82f733a72e37c9c4629adab94c729d9987261cde8.map