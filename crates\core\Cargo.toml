[package]
name = "moduforge-core"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 核心模块"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}
[lib]
name = "mf_core"
path = "src/lib.rs"



[dependencies]
tokio = { workspace=true }
async-channel= {workspace=true}
anyhow= {workspace=true}
thiserror = { workspace=true }
serde = { workspace=true }
serde_json = {workspace=true}
async-trait= {workspace=true}
moduforge-model = {version = "0.4.12", path = "../model"}
moduforge-state = {version = "0.4.12", path = "../state"}
moduforge-transform = {version = "0.4.12", path = "../transform"}
metrics = "0.22.0"
arc-swap = "1.6"
dashmap = "5.5"
quick-xml = { workspace = true }

[dev-dependencies]
rand = "0.8"


