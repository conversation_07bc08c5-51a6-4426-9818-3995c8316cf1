<?xml version="1.0" encoding="UTF-8"?>
<!-- 表格Schema定义 -->
<schema top_node="doc" 
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="https://pricing-dev.oss-cn-hangzhou.aliyuncs.com/moduforge/moduforge-schema.xsd">
  <nodes>
    <!-- 文档根节点 -->
    <node name="doc" group="block">
      <desc>文档根节点</desc>
      <content>(paragraph | table)+</content>
      <marks>_</marks>
    </node>
    
    <!-- 段落节点 -->
    <node name="paragraph" group="block">
      <desc>段落节点</desc>
      <content>inline*</content>
      <marks>strong em</marks>
    </node>
    
    <!-- 表格节点 -->
    <node name="table" group="block">
      <desc>表格节点</desc>
      <content>tablerow+</content>
      <attrs>
        <attr name="border" default="1"/>
        <attr name="width"/>
        <attr name="caption"/>
      </attrs>
    </node>
    
    <!-- 表格行节点 -->
    <node name="tablerow">
      <desc>表格行节点</desc>
      <content>tablecell+</content>
      <attrs>
        <attr name="type" default="data"/>
      </attrs>
    </node>
    
    <!-- 表格单元格节点 -->
    <node name="tablecell">
      <desc>表格单元格节点</desc>
      <content>paragraph*</content>
      <attrs>
        <attr name="colspan" default="1"/>
        <attr name="rowspan" default="1"/>
        <attr name="align" default="left"/>
      </attrs>
    </node>
    
    <!-- 文本节点 -->
    <node name="text">
      <desc>文本节点</desc>
    </node>
  </nodes>
  
  <marks>
    <!-- 粗体标记 -->
    <mark name="strong" group="formatting">
      <desc>粗体文本标记</desc>
      <spanning>true</spanning>
    </mark>
    
    <!-- 斜体标记 -->
    <mark name="em" group="formatting">
      <desc>斜体文本标记</desc>
      <spanning>true</spanning>
    </mark>
  </marks>
</schema>
