{"name": "collaboration-client", "version": "0.1.0", "description": "Frontend client for ModuForge collaboration services.", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["yjs", "moduforge", "collaboration"], "author": "", "license": "ISC", "dependencies": {"yjs": "^13.6.14"}, "devDependencies": {"typescript": "^5.4.5", "vite": "^5.2.11", "vite-plugin-dts": "^3.6.4", "y-websocket": "^2.0.3"}}