[package]
name = "moduforge-rules-template"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 模板规则"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}
[lib]
name = "mf_template"

[dependencies]
moduforge-rules-expression = { path = "../expression", version = "0.4.12" }
thiserror = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }

[features]
default = ["stack-protection", "regex-deprecated"]

stack-protection = ["moduforge-rules-expression/stack-protection"]
regex-lite = ["moduforge-rules-expression/regex-lite"]
regex-deprecated = ["moduforge-rules-expression/regex-deprecated"]