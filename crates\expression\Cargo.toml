[package]
name = "moduforge-rules-expression"
version = {workspace=true}
edition = {workspace=true}
description = "moduforge 表达式规则"
authors = {workspace=true}
license = {workspace=true}
documentation = {workspace=true}
homepage = {workspace=true}
repository = {workspace=true}

[lib]
name = "mf_expression"

[dependencies]
anyhow = { workspace = true }
ahash = { workspace = true }
bumpalo = { workspace = true, features = ["collections"] }
chrono = { workspace = true }
chrono-tz = "0.10"
humantime = { workspace = true }
fastrand = { workspace = true }
once_cell = { workspace = true }
recursive = { workspace = true, optional = true }
regex = { workspace = true, optional = true }
regex-lite = { workspace = true, optional = true }
serde = { workspace = true, features = ["rc", "derive"] }
serde_json = { workspace = true, features = ["arbitrary_precision"] }
strum = { workspace = true }
strum_macros = { workspace = true }
thiserror = { workspace = true }
rust_decimal = { workspace = true, features = ["maths-nopanic"] }
rust_decimal_macros = { workspace = true }
nohash-hasher = "0.2.0"
strsim = "0.11"
iana-time-zone = "0.1"

[dev-dependencies]
criterion = { workspace = true }
csv = "1"
serde_json5 = "0.2"

[features]
default = ["regex-deprecated", "stack-protection"]
regex-lite = ["dep:regex-lite"]
regex-deprecated = ["dep:regex"]
stack-protection = ["dep:recursive"]

[[bench]]
harness = false
name = "lexer"

[[bench]]
harness = false
name = "standard"

[[bench]]
harness = false
name = "unary"

[[bench]]
harness = false
name = "isolate"