# ModuForge-RS 外部项目集成规则
# 适用于引入 ModuForge-RS 作为核心库的项目

## 项目概览
这个项目使用 ModuForge-RS 作为核心状态管理和数据转换框架。ModuForge-RS 是一个基于 Rust 的不可变数据结构和事件驱动架构的框架。

## ModuForge-RS 核心依赖配置

### Cargo.toml 依赖配置
在你的项目中添加以下依赖：

```toml
[dependencies]
# ModuForge-RS 核心组件
moduforge-core = "0.4.12"
moduforge-model = "0.4.12"
moduforge-state = "0.4.12"
moduforge-transform = "0.4.12"
moduforge-rules-engine = "0.4.12"
moduforge-rules-expression = "0.4.12"
moduforge-collaboration = "0.4.12"
moduforge-template = "0.4.12"

# ModuForge-RS 依赖的核心库
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive", "rc"] }
im = { version = "15.1", features = ["serde"] }
anyhow = "1"
thiserror = "2.0.12"
async-trait = "0.1"
tracing = "0.1"
uuid = { version = "1.0", features = ["v4"] }
```

## 核心架构模式

### 1. 运行时初始化模式
使用 ModuForge-RS 的异步运行时：

```rust
use mf_core::{ForgeResult, async_runtime::AsyncRuntime, types::RuntimeOptions};
use mf_state::{init_logging, StateConfig};
use mf_core::middleware::MiddlewareStack;

// 初始化异步运行时
async fn initialize_runtime() -> ForgeResult<AsyncRuntime> {
    // 配置日志系统
    init_logging("info", Some("logs/app.log"))?;
    
    // 创建运行时配置
    let mut options = RuntimeOptions::default();
    options.set_middleware_stack(MiddlewareStack::new());
    
    // 创建状态配置
    let state_config = StateConfig::default();
    
    // 创建异步运行时实例
    AsyncRuntime::new(options, state_config).await
}
```

### 2. 插件和扩展开发模式
通过 Extension 系统创建插件：

```rust
use mf_core::extension::Extension;
use mf_state::{
    plugin::{Plugin, PluginSpec, PluginTrait, StateField},
    resource::Resource,
    state::{State, StateConfig},
    transaction::Transaction,
    error::StateResult,
};
use async_trait::async_trait;
use std::sync::Arc;

// 1. 定义插件状态资源
#[derive(Debug, Clone)]
pub struct MyPluginState {
    pub data: im::HashMap<String, String>,
    pub count: u64,
}

impl Resource for MyPluginState {}

impl MyPluginState {
    pub fn new() -> Self {
        Self {
            data: im::HashMap::new(),
            count: 0,
        }
    }
}

// 2. 实现状态字段管理器
#[derive(Debug)]
pub struct MyStateField;

#[async_trait]
impl StateField for MyStateField {
    async fn init(
        &self,
        _config: &StateConfig,
        _instance: Option<&State>,
    ) -> Arc<dyn Resource> {
        println!("初始化我的插件状态");
        Arc::new(MyPluginState::new())
    }

    async fn apply(
        &self,
        tr: &Transaction,
        value: Arc<dyn Resource>,
        _old_state: &State,
        _new_state: &State,
    ) -> Arc<dyn Resource> {
        if let Ok(state) = value.clone().downcast::<MyPluginState>() {
            let mut new_state = (*state).clone();
            
            // 根据事务元数据更新状态
            if let Some(action) = tr.get_meta::<String>("action") {
                match action.as_str() {
                    "increment" => {
                        new_state.count += 1;
                        println!("插件计数器: {}", new_state.count);
                    }
                    "set_data" => {
                        if let Some(key) = tr.get_meta::<String>("key") {
                            if let Some(val) = tr.get_meta::<String>("value") {
                                new_state.data.insert(key.as_str().to_string(), val.as_str().to_string());
                            }
                        }
                    }
                    _ => {}
                }
            }
            
            Arc::new(new_state)
        } else {
            value
        }
    }
}

// 3. 实现插件行为
#[derive(Debug)]
pub struct MyPlugin;

#[async_trait]
impl PluginTrait for MyPlugin {
    async fn append_transaction(
        &self,
        transactions: &[Transaction],
        _old_state: &State,
        _new_state: &State,
    ) -> StateResult<Option<Transaction>> {
        // 检查事务并生成附加事务
        for tr in transactions {
            if let Some(action) = tr.get_meta::<String>("action") {
                if action.as_str() == "my_action" {
                    let mut new_tr = Transaction::new();
                    new_tr.set_meta("generated_by", "my_plugin");
                    new_tr.set_meta("action", "increment");
                    return Ok(Some(new_tr));
                }
            }
        }
        Ok(None)
    }

    async fn filter_transaction(
        &self,
        transaction: &Transaction,
        _state: &State,
    ) -> bool {
        // 过滤逻辑：拒绝某些操作
        if let Some(action) = transaction.get_meta::<String>("action") {
            return action.as_str() != "forbidden_action";
        }
        true
    }
}

// 4. 创建完整的扩展
pub fn create_my_extension() -> Extension {
    let mut extension = Extension::new();
    
    // 创建插件
    let plugin = Plugin::new(PluginSpec {
        key: ("my_plugin".to_string(), "v1".to_string()),
        state_field: Some(Arc::new(MyStateField)),
        tr: Some(Arc::new(MyPlugin)),
        priority: 10,
    });
    
    extension.add_plugin(Arc::new(plugin));
    extension
}

// 5. 在运行时中使用扩展
async fn create_runtime_with_plugin() -> ForgeResult<AsyncRuntime> {
    let mut options = RuntimeOptions::default();
    
    // 添加扩展到运行时选项
    options.add_extension(mf_core::types::Extensions::E(create_my_extension()));
    
    let state_config = StateConfig::default();
    AsyncRuntime::new(options, state_config).await
}

### 3. 节点和文档操作模式

```rust
use mf_model::{
    node::Node as ModelNode,
    node_type::{NodeSpec, NodeEnum},
    attrs::Attrs,
    types::NodeId,
    tree::Tree,
    node_pool::NodePool,
};
use mf_transform::{
    node_step::{AddNodeStep, RemoveNodeStep},
    attr_step::AttrStep,
};
use std::sync::Arc;

// 创建文档节点
fn create_document_nodes() -> NodePool {
    // 创建根节点
    let root_node = ModelNode::new(
        "root",
        "document".to_string(),
        Attrs::default(),
        vec![],
        vec![]
    );
    
    // 创建段落节点
    let paragraph_node = ModelNode::new(
        "para_1",
        "paragraph".to_string(),
        Attrs::default(),
        vec![],
        vec![]
    );
    
    // 构建节点枚举
    let node_enum = NodeEnum(root_node, vec![
        NodeEnum(paragraph_node, vec![])
    ]);
    
    // 创建树和节点池
    let tree = Tree::from(node_enum);
    NodePool::new(Arc::new(tree))
}

// 操作文档
async fn manipulate_document(runtime: &mut AsyncRuntime) -> ForgeResult<()> {
    let mut transaction = Transaction::new();
    
    // 添加新节点
    let new_node = ModelNode::new(
        "new_para",
        "paragraph".to_string(),
        Attrs::default(),
        vec![],
        vec![]
    );
    
    let add_step = AddNodeStep::new(new_node, Some(NodeId::from("root")));
    transaction.add_step(Box::new(add_step));
    
    // 设置事务元数据
    transaction.set_meta("action", "add_paragraph");
    transaction.set_meta("user_id", "user_123");
    
    // 执行事务
    runtime.apply_transaction(transaction).await
}
```

### 4. 中间件开发模式

```rust
use mf_core::middleware::{Middleware, MiddlewareStack};
use async_trait::async_trait;

#[derive(Debug)]
struct LoggingMiddleware {
    name: String,
}

impl LoggingMiddleware {
    pub fn new() -> Self {
        Self { name: "LoggingMiddleware".to_string() }
    }
}

#[async_trait]
impl Middleware for LoggingMiddleware {
    fn name(&self) -> String {
        self.name.clone()
    }

    async fn before_dispatch(
        &self,
        transaction: &mut Transaction,
    ) -> ForgeResult<()> {
        println!("🔍 [{}] 事务处理开始 - ID: {}", self.name, transaction.id);
        Ok(())
    }

    async fn after_dispatch(
        &self,
        state: Option<Arc<State>>,
        transactions: &[Transaction],
    ) -> ForgeResult<Option<Transaction>> {
        println!("✅ [{}] 事务处理完成", self.name);
        Ok(None)
    }
}

// 使用中间件
fn setup_middleware() -> MiddlewareStack {
    let mut middleware_stack = MiddlewareStack::new();
    middleware_stack.add(LoggingMiddleware::new());
    middleware_stack
}
```

### 5. 事件处理模式

```rust
use mf_core::event::{Event, EventHandler, EventBus};

#[derive(Debug)]
struct StateChangeHandler;

#[async_trait]
impl EventHandler<Event> for StateChangeHandler {
    async fn handle(&self, event: &Event) -> ForgeResult<()> {
        match event {
            Event::Create(state) => {
                println!("🎉 状态创建: 版本 {}", state.version);
            }
            Event::TrApply(tr_id, transactions, state) => {
                println!("📝 事务应用: ID {}, 版本 {}", tr_id, state.version);
            }
            Event::Destroy => {
                println!("🗑️ 状态销毁");
            }
            Event::Stop => {
                println!("⏹️ 状态停止");
            }
        }
        Ok(())
    }
}

// 设置事件处理
fn setup_event_handling() -> EventBus<Event> {
    let event_bus = EventBus::<Event>::new();
    event_bus.add_event_handler(Arc::new(StateChangeHandler)).unwrap();
    event_bus.start_event_loop();
    event_bus
}
```

## 代码风格和最佳实践

### 错误处理
- 使用 `ForgeResult<T>` 作为返回类型
- 使用 `?` 操作符进行错误传播
- 提供有意义的错误消息

### 异步编程
- 所有 I/O 操作使用 `async/await`
- 使用 `Arc<T>` 进行共享所有权
- 避免在异步上下文中阻塞

### 内存管理
- 优先使用不可变数据结构 (`im::HashMap`, `im::Vector`)
- 使用 `Arc` 而非 `Rc` 在多线程环境中
- 避免不必要的克隆操作

### 测试
```rust
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_plugin_integration() {
        let runtime = initialize_runtime().await.unwrap();
        // 测试逻辑
        assert!(runtime.is_healthy());
    }
}
```

## 项目结构建议
```
your-project/
├── src/
│   ├── lib.rs              # 库入口
│   ├── plugins/            # 插件模块
│   │   ├── mod.rs
│   │   ├── custom_plugin.rs
│   │   └── business_plugin.rs
│   ├── extensions/         # 扩展模块
│   │   ├── mod.rs
│   │   └── custom_extensions.rs
│   ├── middleware/         # 中间件模块
│   │   ├── mod.rs
│   │   └── auth_middleware.rs
│   ├── handlers/           # 业务处理器
│   │   ├── mod.rs
│   │   └── document_handler.rs
│   └── config/             # 配置模块
│       ├── mod.rs
│       └── app_config.rs
├── Cargo.toml
└── README.md
```

## 性能优化建议
- 使用 `Arc<T>` 在异步上下文中共享所有权
- 优先借用而不是克隆，特别是对于不可变数据
- 使用 `tokio::spawn` 处理 CPU 密集型任务
- 在热路径中最小化内存分配
- 考虑使用 `smallvec` 处理小集合
- 使用延迟求值处理昂贵的计算

## 测试模式
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use mf_state::{State, StateConfig};
    use tokio_test;

    #[tokio::test]
    async fn test_state_initialization() {
        let config = StateConfig::default();
        let state = State::new(config).await.unwrap();
        
        assert!(state.version() > 0);
    }

    #[tokio::test]
    async fn test_plugin_integration() {
        // 插件集成测试
    }
}
```

## 避免的反模式
- 在生产代码中使用 `unwrap()` 或 `expect()` 而不提供合理理由
- 忽略编译器警告
- 在异步代码中进行阻塞操作
- 大型函数（建议拆分为更小的函数）
- 深层嵌套（使用早期返回和保护子句）
- 可变全局状态
- 忽略 ModuForge-RS 的事件驱动架构

## 日志和调试
```rust
use mf_state::{info, debug, warn, error, init_logging};

// 初始化日志系统
init_logging("debug", Some("logs/app.log")).unwrap();

// 使用日志宏
info!("应用启动");
debug!("调试信息: {:?}", data);
warn!("警告: 配置可能不正确");
error!("错误: 操作失败");
```

## 插件开发指南
详细的插件开发文档请参考 `plugin-development-guide.md`，包含：
- 完整的插件结构（Resource, StateField, PluginTrait）
- 状态管理和事务处理
- 插件间通信机制
- 资源共享和性能优化
- 完整的测试示例

## AI 助手指导原则
当协助此项目时：
1. 始终考虑 ModuForge-RS 的不可变数据结构范式
2. 建议事件驱动的状态变更解决方案
3. 推荐适当的错误处理模式
4. 考虑建议的性能影响
5. 保持与现有架构模式的一致性
6. 为新功能建议测试
7. 考虑向后兼容性
8. 遵循 Rust 最佳实践和惯用语法
9. 优先考虑类型安全和内存安全
10. 在建议更改时考虑插件和中间件架构
11. **插件开发时使用正确的三层结构：Resource + StateField + PluginTrait**
12. **使用事务元数据进行插件间通信和状态管理** 