globalThis.dayjs=function(){"use strict";var t="millisecond",n="second",e="minute",r="hour",i="week",s="month",u="quarter",o="year",a="date",f="Invalid Date",h=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,c=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,d={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(t){var n=["th","st","nd","rd"],e=t%100;return"["+t+(n[(e-20)%10]||n[e]||n[0])+"]"}},l=function(t,n,e){var r=String(t);return!r||r.length>=n?t:""+Array(n+1-r.length).join(e)+t},_="en",$={};$[_]=d;var y=function(t){return t instanceof g},v=function t(n,e,r){var i;if(!n)return _;if("string"==typeof n){var s=n.toLowerCase();$[s]&&(i=s),e&&($[s]=e,i=s);var u=n.split("-");if(!i&&u.length>1)return t(u[0])}else{var o=n.name;$[o]=n,i=o}return!r&&i&&(_=i),i||!r&&_},m=function(t,n){if(y(t))return t.clone();var e="object"==typeof n?n:{};return e.date=t,e.args=arguments,new g(e)},M={s:l,z:function(t){var n=-t.utcOffset(),e=Math.abs(n);return(n<=0?"+":"-")+l(Math.floor(e/60),2,"0")+":"+l(e%60,2,"0")},m:function t(n,e){if(n.date()<e.date())return-t(e,n);var r=12*(e.year()-n.year())+(e.month()-n.month()),i=n.clone().add(r,s),u=e-i<0,o=n.clone().add(r+(u?-1:1),s);return+(-(r+(e-i)/(u?i-o:o-i))||0)},a:function(t){return t<0?Math.ceil(t)||0:Math.floor(t)},p:function(f){return({M:s,y:o,w:i,d:"day",D:a,h:r,m:e,s:n,ms:t,Q:u})[f]||String(f||"").toLowerCase().replace(/s$/,"")},u:function(t){return void 0===t}};M.l=v,M.i=y,M.w=function(t,n){return m(t,{locale:n.$L,utc:n.$u,x:n.$x,$offset:n.$offset})};var g=function(){function d(t){this.$L=v(t.locale,null,!0),this.parse(t)}var l=d.prototype;return l.parse=function(t){this.$d=function(t){var n=t.date,e=t.utc;if(null===n)return new Date(NaN);if(M.u(n))return new Date;if(n instanceof Date)return new Date(n);if("string"==typeof n&&!/Z$/i.test(n)){var r=n.match(h);if(r){var i=r[2]-1||0,s=(r[7]||"0").substring(0,3);return e?new Date(Date.UTC(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)):new Date(r[1],i,r[3]||1,r[4]||0,r[5]||0,r[6]||0,s)}}return new Date(n)}(t),this.$x=t.x||{},this.init()},l.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},l.$utils=function(){return M},l.isValid=function(){return this.$d.toString()!==f},l.isSame=function(t,n){var e=m(t);return this.startOf(n)<=e&&e<=this.endOf(n)},l.isAfter=function(t,n){return m(t)<this.startOf(n)},l.isBefore=function(t,n){return this.endOf(n)<m(t)},l.$g=function(t,n,e){return M.u(t)?this[n]:this.set(e,t)},l.unix=function(){return Math.floor(this.valueOf()/1e3)},l.valueOf=function(){return this.$d.getTime()},l.startOf=function(t,u){var f=this,h=!!M.u(u)||u,c=M.p(t),d=function(t,n){var e=M.w(f.$u?Date.UTC(f.$y,n,t):new Date(f.$y,n,t),f);return h?e:e.endOf("day")},l=function(t,n){return M.w(f.toDate()[t].apply(f.toDate("s"),(h?[0,0,0,0]:[23,59,59,999]).slice(n)),f)},_=this.$W,$=this.$M,y=this.$D,v="set"+(this.$u?"UTC":"");switch(c){case o:return h?d(1,0):d(31,11);case s:return h?d(1,$):d(0,$+1);case i:var m=this.$locale().weekStart||0,g=(_<m?_+7:_)-m;return d(h?y-g:y+(6-g),$);case"day":case a:return l(v+"Hours",0);case r:return l(v+"Minutes",1);case e:return l(v+"Seconds",2);case n:return l(v+"Milliseconds",3);default:return this.clone()}},l.endOf=function(t){return this.startOf(t,!1)},l.$set=function(i,u){var f,h=M.p(i),c="set"+(this.$u?"UTC":""),d=((f={}).day=c+"Date",f[a]=c+"Date",f[s]=c+"Month",f[o]=c+"FullYear",f[r]=c+"Hours",f[e]=c+"Minutes",f[n]=c+"Seconds",f[t]=c+"Milliseconds",f)[h],l="day"===h?this.$D+(u-this.$W):u;if(h===s||h===o){var _=this.clone().set(a,1);_.$d[d](l),_.init(),this.$d=_.set(a,Math.min(this.$D,_.daysInMonth())).$d}else d&&this.$d[d](l);return this.init(),this},l.set=function(t,n){return this.clone().$set(t,n)},l.get=function(t){return this[M.p(t)]()},l.add=function(t,u){var a,f=this;t=Number(t);var h=M.p(u),c=function(n){var e=m(f);return M.w(e.date(e.date()+Math.round(n*t)),f)};if(h===s)return this.set(s,this.$M+t);if(h===o)return this.set(o,this.$y+t);if("day"===h)return c(1);if(h===i)return c(7);var d=((a={})[e]=6e4,a[r]=36e5,a[n]=1e3,a)[h]||1,l=this.$d.getTime()+t*d;return M.w(l,this)},l.subtract=function(t,n){return this.add(-1*t,n)},l.format=function(t){var n=this,e=this.$locale();if(!this.isValid())return e.invalidDate||f;var r=t||"YYYY-MM-DDTHH:mm:ssZ",i=M.z(this),s=this.$H,u=this.$m,o=this.$M,a=e.weekdays,h=e.months,d=function(t,e,i,s){return t&&(t[e]||t(n,r))||i[e].slice(0,s)},l=function(t){return M.s(s%12||12,t,"0")},_=e.meridiem||function(t,n,e){var r=t<12?"AM":"PM";return e?r.toLowerCase():r},$={YY:String(this.$y).slice(-2),YYYY:this.$y,M:o+1,MM:M.s(o+1,2,"0"),MMM:d(e.monthsShort,o,h,3),MMMM:d(h,o),D:this.$D,DD:M.s(this.$D,2,"0"),d:String(this.$W),dd:d(e.weekdaysMin,this.$W,a,2),ddd:d(e.weekdaysShort,this.$W,a,3),dddd:a[this.$W],H:String(s),HH:M.s(s,2,"0"),h:l(1),hh:l(2),a:_(s,u,!0),A:_(s,u,!1),m:String(u),mm:M.s(u,2,"0"),s:String(this.$s),ss:M.s(this.$s,2,"0"),SSS:M.s(this.$ms,3,"0"),Z:i};return r.replace(c,function(t,n){return n||$[t]||i.replace(":","")})},l.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},l.diff=function(t,a,f){var h,c=M.p(a),d=m(t),l=(d.utcOffset()-this.utcOffset())*6e4,_=this-d,$=M.m(this,d);return $=((h={})[o]=$/12,h[s]=$,h[u]=$/3,h[i]=(_-l)/6048e5,h.day=(_-l)/864e5,h[r]=_/36e5,h[e]=_/6e4,h[n]=_/1e3,h)[c]||_,f?$:M.a($)},l.daysInMonth=function(){return this.endOf(s).$D},l.$locale=function(){return $[this.$L]},l.locale=function(t,n){if(!t)return this.$L;var e=this.clone(),r=v(t,n,!0);return r&&(e.$L=r),e},l.clone=function(){return M.w(this.$d,this)},l.toDate=function(){return new Date(this.valueOf())},l.toJSON=function(){return this.isValid()?this.toISOString():null},l.toISOString=function(){return this.$d.toISOString()},l.toString=function(){return this.$d.toUTCString()},d}(),p=g.prototype;return m.prototype=p,[["$ms",t],["$s",n],["$m",e],["$H",r],["$W","day"],["$M",s],["$y",o],["$D",a]].forEach(function(t){p[t[1]]=function(n){return this.$g(n,t[0],t[1])}}),m.extend=function(t,n){return t.$i||(t(n,g,m),t.$i=!0),m},m.locale=v,m.isDayjs=y,m.unix=function(t){return m(1e3*t)},m.en=$[_],m.Ls=$,m.p={},m}();