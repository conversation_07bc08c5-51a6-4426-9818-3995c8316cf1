/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACol: typeof import('ant-design-vue/es')['Col']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    AInputSearch: typeof import('ant-design-vue/es')['InputSearch']
    ALayout: typeof import('ant-design-vue/es')['Layout']
    ALayoutContent: typeof import('ant-design-vue/es')['LayoutContent']
    ALayoutHeader: typeof import('ant-design-vue/es')['LayoutHeader']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASpace: typeof import('ant-design-vue/es')['Space']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ATable: typeof import('ant-design-vue/es')['Table']
    ATag: typeof import('ant-design-vue/es')['Tag']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
