# ModuForge-RS 外部项目集成规则
# 基于 Rust 的状态管理和数据转换框架

## 项目概述
ModuForge-RS 是一个基于 Rust 的现代化状态管理和数据转换框架，采用不可变数据结构和事件驱动架构。该框架提供了完整的插件系统、中间件支持、规则引擎集成和协作功能。

## 核心架构组件

### 1. 核心模块 (moduforge-core)
- **异步处理器**: 提供高性能的异步任务处理能力
- **事件系统**: 类型安全的事件分发和处理机制
- **扩展机制**: 灵活的插件和扩展加载系统
- **中间件支持**: 可配置的请求/响应处理管道
- **流程控制**: 同步和异步流程管理

### 2. 数据模型 (moduforge-model)
- **节点系统**: 层次化的文档节点结构
- **标记系统**: 文档格式化和属性标记
- **属性系统**: 类型安全的属性管理
- **模式定义**: 文档结构验证和约束
- **内容匹配**: 智能内容验证和匹配

### 3. 状态管理 (moduforge-state)
- **不可变状态**: 基于 im-rs 的持久化数据结构
- **事务处理**: ACID 兼容的事务系统
- **资源管理**: 全局资源表和生命周期管理
- **插件系统**: 动态插件加载和状态隔离
- **日志系统**: 结构化日志和性能监控

### 4. 规则引擎 (moduforge-rules-engine)
- **决策引擎**: 基于 GoRules JDM 标准的业务规则引擎
- **表达式语言**: 高性能的表达式求值系统
- **函数系统**: 内置函数、自定义函数和方法支持
- **加载器系统**: 文件系统、内存、闭包等多种加载方式
- **图执行**: 复杂的决策图执行和追踪

### 5. 表达式系统 (moduforge-rules-expression)
- **轻量级语言**: 专为高性能设计的表达式语言
- **类型系统**: 完整的类型检查和推断
- **自定义函数**: 支持用户定义的扩展函数
- **智能感知**: 代码补全和类型提示
- **WASM 支持**: 可编译到 WebAssembly

### 6. 数据转换 (moduforge-transform)
- **节点操作**: 添加、移动、删除、替换节点
- **标记操作**: 标记的添加和删除
- **属性更新**: 批量属性修改
- **补丁系统**: 增量更新和内存优化
- **批量操作**: 高效的批量转换处理

### 7. 协作系统 (moduforge-collaboration)
- **实时同步**: 基于 Yrs 的实时协作
- **WebSocket 服务**: 高性能的 WebSocket 服务器
- **冲突解决**: 自动冲突检测和解决
- **房间管理**: 多房间协作支持
- **状态映射**: 文档状态到协作状态的映射

### 8. 模板系统 (moduforge-template)
- **模板渲染**: 基于表达式的模板系统
- **词法分析**: 高效的模板解析
- **上下文支持**: 动态上下文变量注入

## 技术栈和依赖

### 核心依赖
```toml
[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }
async-trait = "0.1"

# 不可变数据结构
im = { version = "15.1", features = ["serde"] }

# 序列化
serde = { version = "1.0", features = ["derive", "rc"] }
serde_json = "1.0"

# 错误处理
anyhow = "1"
thiserror = "2.0.12"

# 并发和同步
dashmap = "6.1.0"
async-channel = "2.3.1"
crossbeam = "0.8"
parking_lot = "0.12"

# 日志和监控
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 工具库
uuid = { version = "1.0", features = ["v4"] }
chrono = "0.4"
rayon = "1.8"
lru = "0.14.0"
ahash = "0.8"
rust_decimal = "1"

# ModuForge-RS 核心包
moduforge-core = "0.4.12"
moduforge-state = "0.4.12"
moduforge-model = "0.4.12"
moduforge-transform = "0.4.12"
moduforge-rules-engine = "0.4.12"
moduforge-rules-expression = "0.4.12"
moduforge-collaboration = "0.4.12"
moduforge-template = "0.4.12"
```

## 代码风格指南

### Rust 约定
- 遵循标准 Rust 命名约定（snake_case 变量/函数，PascalCase 类型）
- 使用 `clippy` 警告和建议
- 优先使用显式错误处理而非 panic
- 使用 `Result<T, E>` 进行可失败操作
- 为所有自定义类型实现 `Debug`、`Clone`、`PartialEq` 派生
- 除非有特殊原因，否则为所有自定义类型使用 `#[derive(Debug)]`

### 项目特定模式
- 使用 `im::HashMap` 和 `im::Vector` 作为不可变集合
- 事件驱动架构：所有状态变更都应发出事件
- 基于事务的操作：将相关变更分组到事务中
- 插件架构：实现可扩展的插件系统
- 中间件模式：使用中间件处理横切关注点

### 错误处理
- 使用 `thiserror` 定义自定义错误类型
- 使用 `anyhow` 进行应用级错误处理
- 当已知错误原因时，优先使用 `Result` 而非 `Option`
- 始终提供有意义的错误消息
- 使用 `?` 操作符进行错误传播

### 内存管理
- 尽可能优先使用借用而非克隆
- 在异步上下文中使用 `Arc<T>` 进行共享所有权
- 在单线程上下文中使用 `Rc<T>` 进行共享所有权
- 必要时明确指定生命周期

## 架构模式

### 状态管理
- 使用 im-rs 集合的不可变状态
- 通过事件进行状态转换
- 基于快照的状态持久化
- 时间旅行调试能力

### 事件系统
- 事件驱动架构
- 类型安全的事件分发
- 用于状态重建的事件溯源
- 事件重放能力

### 插件系统
- 动态插件加载
- 插件生命周期管理
- 插件依赖解析
- 插件隔离和沙箱化

### 事务模型
- ACID 兼容的事务
- 回滚能力
- 事务日志记录
- 并发事务处理

## 代码组织

### 模块结构
```
src/
├── core/           # 核心框架组件
├── events/         # 事件系统实现
├── plugins/        # 插件系统
├── middleware/     # 中间件组件
├── state/          # 状态管理
├── transactions/   # 事务处理
├── rules/          # 规则引擎集成
└── utils/          # 工具函数
```

### 文件命名
- 使用反映模块用途的描述性名称
- 在模块中分组相关功能
- 使用 `mod.rs` 进行模块声明
- 保持文件大小可管理（尽可能在 500 行以下）

## 测试指南
- 为所有公共 API 编写单元测试
- 在适当时使用 quickcheck 进行基于属性的测试
- 端到端工作流的集成测试
- 模拟外部依赖
- 测试错误条件和边缘情况
- 使用 `#[cfg(test)]` 标记仅测试代码

## 性能考虑
- 在热路径中最小化分配
- 尽可能使用零拷贝操作
- 在优化前进行性能分析
- 考虑对小型集合使用 `smallvec`
- 对不可变字符串使用 `Box<str>` 而非 `String`
- 对昂贵计算使用惰性求值

## 文档
- 使用 `///` 进行公共 API 文档
- 在文档中包含示例
- 记录错误条件
- 解释复杂算法
- 保持 README.md 与当前功能同步

## 依赖管理
- 最小化外部依赖
- 在 Cargo.lock 中固定依赖版本
- 定期审计依赖的安全性
- 优先选择有良好文档的维护良好的 crate
- 对简单功能考虑内部替代方案

## 异步编程
- 对 I/O 操作使用 `async/await`
- 对 CPU 密集型任务优先使用 `tokio::spawn`
- 对共享可变状态使用 `Arc<Mutex<T>>` 或 `Arc<RwLock<T>>`
- 避免在异步上下文中进行阻塞操作
- 使用通道进行任务间通信

## 安全考虑
- 验证所有外部输入
- 使用类型安全 API 防止常见错误
- 实现适当的身份验证和授权
- 记录安全相关事件
- 使用安全的随机数生成
- 避免在日志中存储敏感数据

## 常见反模式避免
- 在生产代码中无理由使用 `unwrap()` 或 `expect()`
- 忽略编译器警告
- 大型函数（优先分解为较小函数）
- 深度嵌套（使用早期返回和守卫子句）
- 可变全局状态
- 在异步代码中进行阻塞操作

## 特定框架功能

### 节点系统
- 层次化节点结构
- 基于属性的节点属性
- 基于标记的节点标记
- 节点验证和类型检查

### 规则引擎集成
- 基于 YAML 的规则配置
- 动态规则评估
- 规则组合和链接
- 自定义规则验证器

### 中间件链
- 请求/响应处理管道
- 可配置的中间件排序
- 错误处理和恢复
- 性能监控

### 插件架构
- 插件发现和加载
- 插件配置管理
- 插件生命周期钩子
- 插件通信协议

## AI 助手指南
在帮助处理此代码库时：
1. 始终考虑不可变数据结构范式
2. 为状态变更建议事件驱动解决方案
3. 推荐适当的错误处理模式
4. 考虑建议的性能影响
5. 保持与现有架构模式的一致性
6. 为新功能建议测试
7. 考虑向后兼容性
8. 遵循 Rust 最佳实践和惯用法
9. 优先考虑类型安全和内存安全
10. 在建议变更时考虑插件和中间件架构

## 集成最佳实践

### 初始化框架
```rust
use mf_core::{AsyncRuntime, RuntimeOptions};
use mf_state::{State, StateConfig};
use mf_core::middleware::MiddlewareStack;

// 创建运行时配置
let mut options = RuntimeOptions::default();
options.set_middleware_stack(MiddlewareStack::new());

// 创建状态配置
let state_config = StateConfig::default();

// 初始化运行时
let runtime = AsyncRuntime::new(options, state_config).await?;
```

### 节点定义和使用
```rust
use mf_core::node::Node;
use mf_model::{
    node::Node as ModelNode, 
    attrs::Attrs, 
    types::NodeId,
    node_type::{NodeSpec, NodeEnum},
    schema::AttributeSpec,
    tree::Tree,
    node_pool::NodePool
};
use mf_macro::node;
use std::collections::HashMap;
use serde_json::Value;
use std::sync::Arc;

// 1. 使用宏定义节点（适用于简单节点）
let paragraph_node = node!(
    "paragraph",
    "段落节点",
    "block",
    "level" => 1,
    "style" => "normal"
);

// 2. 手动创建核心节点规范
let mut attrs = HashMap::new();
attrs.insert(
    "align".to_string(),
    AttributeSpec { default: Some(Value::String("left".to_string())) }
);
attrs.insert(
    "indent".to_string(),
    AttributeSpec { default: Some(Value::Number(serde_json::Number::from(0))) }
);

let spec = NodeSpec {
    content: Some("inline".to_string()),
    marks: None,
    attrs: Some(attrs),
    desc: Some("段落节点，包含文本内容".to_string()),
    ..Default::default()
};

let core_node = Node::create("paragraph", spec);

// 3. 创建模型节点（用于文档树）
let model_node = ModelNode::new(
    "text_1",
    "text".to_string(),
    Attrs::default(),
    vec![], // 子节点ID列表
    vec![]  // 标记列表
);

// 4. 从节点枚举创建树和节点池
let root_node = ModelNode::new(
    "root",
    "document".to_string(),
    Attrs::default(),
    vec![],
    vec![]
);

// 创建节点枚举（包含层次结构）
let node_enum = NodeEnum(root_node, vec![
    NodeEnum(model_node, vec![])
]);

// 从节点枚举创建树
let tree = Tree::from(node_enum);

// 创建节点池
let node_pool = NodePool::new(Arc::new(tree));

// 5. 查询和操作节点
let root_node = node_pool.root();
let children = node_pool.children(&root_node.id);
let node_by_id = node_pool.get_node(&NodeId::from("text_1"));
```

### 创建插件
```rust
use mf_state::plugin::{Plugin, PluginSpec, PluginTrait, StateField};
use mf_core::extension::Extension;

#[derive(Debug)]
struct MyPlugin;

#[async_trait]
impl PluginTrait for MyPlugin {
    async fn append_transaction(
        &self,
        transactions: &[Transaction],
        old_state: &State,
        new_state: &State,
    ) -> StateResult<Option<Transaction>> {
        // 插件逻辑
        Ok(None)
    }
}

// 创建扩展
let mut extension = Extension::new();
let plugin = Plugin::new(PluginSpec {
    key: ("my_plugin".to_string(), "v1".to_string()),
    tr: Some(Arc::new(MyPlugin)),
    priority: 10,
    state_field: None,
});
extension.add_plugin(Arc::new(plugin));
```

### 创建中间件
```rust
use mf_core::middleware::{Middleware, MiddlewareStack};
use mf_state::{State, Transaction};
use async_trait::async_trait;
use std::sync::Arc;

#[derive(Debug)]
struct LoggingMiddleware {
    name: String,
}

impl LoggingMiddleware {
    pub fn new() -> Self {
        Self { name: "LoggingMiddleware".to_string() }
    }
}

#[async_trait]
impl Middleware for LoggingMiddleware {
    fn name(&self) -> String {
        self.name.clone()
    }

    async fn before_dispatch(
        &self,
        transaction: &mut Transaction,
    ) -> ForgeResult<()> {
        println!("🔍 [{}] 事务处理开始 - ID: {}", self.name, transaction.id);
        Ok(())
    }

    async fn after_dispatch(
        &self,
        state: Option<Arc<State>>,
        transactions: &[Transaction],
    ) -> ForgeResult<Option<Transaction>> {
        println!("✅ [{}] 事务处理完成", self.name);
        Ok(None)
    }
}

// 使用中间件
let mut middleware_stack = MiddlewareStack::new();
middleware_stack.add(LoggingMiddleware::new());

// 添加到运行时配置
let mut options = RuntimeOptions::default();
options.set_middleware_stack(middleware_stack);
```

### 创建事件处理器
```rust
use mf_core::event::{Event, EventHandler, EventBus};
use mf_state::State;
use async_trait::async_trait;
use std::sync::Arc;

#[derive(Debug)]
struct StateChangeHandler;

#[async_trait]
impl EventHandler<Event> for StateChangeHandler {
    async fn handle(&self, event: &Event) -> ForgeResult<()> {
        match event {
            Event::Create(state) => {
                println!("🎉 状态创建: 版本 {}", state.version);
            }
            Event::TrApply(tr_id, transactions, state) => {
                println!("📝 事务应用: ID {}, 版本 {}", tr_id, state.version);
            }
            Event::Destroy => {
                println!("🗑️ 状态销毁");
            }
            Event::Stop => {
                println!("⏹️ 状态停止");
            }
        }
        Ok(())
    }
}

// 创建事件总线
let event_bus = EventBus::<Event>::new();

// 添加事件处理器
event_bus.add_event_handler(Arc::new(StateChangeHandler))?;

// 启动事件循环
event_bus.start_event_loop();

// 广播事件
event_bus.broadcast(Event::Create(Arc::new(state))).await?;
```

### 使用规则引擎
```rust
use mf_rules_engine::{DecisionEngine, EvaluationOptions};
use mf_rules_engine::loader::{FilesystemLoader, FilesystemLoaderOptions};

// 创建决策引擎
let engine = DecisionEngine::new(FilesystemLoader::new(FilesystemLoaderOptions {
    root: "/path/to/decisions",
    keep_in_memory: true,
}));

// 评估决策
let context = serde_json::json!({ "input": 42 });
let result = engine.evaluate("my_decision.json", &context).await?;
```

### 表达式求值
```rust
use mf_rules_expression::{evaluate_expression, Variable};
use serde_json::json;

let context = json!({ "value": 100, "tax_rate": 0.1 });
let result = evaluate_expression("value * tax_rate", context.into())?;
```

### 事务处理
```rust
use mf_state::{Transaction, State};
use mf_transform::{Transform, node_step::AddNodeStep};
use mf_model::{node::Node, types::NodeId};

// 创建事务
let mut transaction = Transaction::new();

// 添加节点步骤
let node = Node::new("new_node", "paragraph".to_string(), Attrs::default(), vec![], vec![]);
let add_step = AddNodeStep::new(node, Some(NodeId::from("parent_node")));
transaction.add_step(add_step);

// 设置事务元数据
transaction.set_meta("action", "add_paragraph");
transaction.set_meta("user_id", "user_123");

// 执行事务
let result = runtime.apply_transaction(transaction).await?;
```

## 性能优化建议

### 状态管理
- 使用不可变数据结构减少克隆开销
- 实现增量更新以减少内存使用
- 使用快照进行状态持久化
- 实现状态分片以支持大规模数据

### 异步处理
- 使用适当的并发级别
- 实现背压机制防止内存溢出
- 使用连接池管理资源
- 实现超时和重试机制

### 缓存策略
- 对频繁访问的数据实现缓存
- 使用 LRU 缓存管理内存使用
- 实现缓存失效策略
- 考虑分布式缓存以支持扩展

## 监控和调试

### 日志记录
```rust
use mf_state::init_logging;

// 初始化日志系统
init_logging("info", Some("logs/app.log"))?;
```

### 性能指标
- 使用内置的指标收集
- 监控事务处理时间
- 跟踪内存使用情况
- 监控插件执行时间

### 调试工具
- 使用时间旅行调试
- 实现状态快照
- 使用事件重放功能
- 启用详细日志记录

## 部署考虑

### 配置管理
- 使用环境变量进行配置
- 实现配置验证
- 支持配置热重载
- 使用配置模板

### 容器化
- 创建多阶段 Dockerfile
- 优化镜像大小
- 实现健康检查
- 配置资源限制

### 扩展性
- 实现水平扩展
- 使用负载均衡
- 实现服务发现
- 配置自动扩缩容 