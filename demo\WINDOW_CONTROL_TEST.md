# 🔧 窗口控制功能测试指南

## ✅ 修复完成的问题

### 1. 主应用最小化问题
**修复内容**:
- ✅ 增强了窗口对象初始化检查
- ✅ 添加了详细的错误处理和日志
- ✅ 实现了窗口状态监听和同步
- ✅ 改进了用户反馈机制

### 2. 概算模块窗口控制问题
**修复内容**:
- ✅ 添加了 `@tauri-apps/api` 依赖
- ✅ 实现了完整的窗口控制逻辑
- ✅ 配置了窗口状态监听
- ✅ 增强了错误处理和用户反馈

## 🧪 测试步骤

### 测试前准备
确保两个服务都在运行：
```bash
# 终端1: 概算模块
cd packages/rough-estimate
npm run dev
# 应该看到: http://localhost:5174/

# 终端2: 主应用
npm run tauri:dev
# 应该看到主应用窗口打开
```

### 主应用窗口控制测试

#### 1. 测试拖动功能
- [ ] 点击紫色标题栏区域（避开按钮和用户信息）
- [ ] 拖动窗口到不同位置
- [ ] **预期**: 窗口应该跟随鼠标移动

#### 2. 测试最小化功能
- [ ] 点击最小化按钮 (➖ 图标)
- [ ] **预期**: 窗口最小化到任务栏
- [ ] 从任务栏点击恢复窗口
- [ ] **预期**: 窗口正常恢复

#### 3. 测试最大化功能
- [ ] 点击最大化按钮 (⬜ 图标)
- [ ] **预期**: 窗口最大化填满屏幕，按钮变为还原图标 (🗗)
- [ ] 再次点击还原按钮
- [ ] **预期**: 窗口恢复到原始大小

#### 4. 测试关闭功能
- [ ] 点击关闭按钮 (❌ 图标，悬停时变红)
- [ ] **预期**: 整个应用关闭

### 概算模块窗口控制测试

#### 1. 打开概算模块
- [ ] 在主应用中点击"概算"模块卡片
- [ ] **预期**: 新窗口打开，显示概算管理系统

#### 2. 测试拖动功能
- [ ] 点击蓝色标题栏区域（避开按钮）
- [ ] 拖动窗口到不同位置
- [ ] **预期**: 窗口应该跟随鼠标移动

#### 3. 测试最小化功能
- [ ] 点击概算窗口的最小化按钮
- [ ] **预期**: 概算窗口最小化到任务栏
- [ ] 从任务栏恢复概算窗口
- [ ] **预期**: 概算窗口正常恢复

#### 4. 测试最大化功能
- [ ] 点击概算窗口的最大化按钮
- [ ] **预期**: 概算窗口最大化，按钮图标变化
- [ ] 再次点击还原按钮
- [ ] **预期**: 概算窗口恢复原始大小

#### 5. 测试关闭功能
- [ ] 点击概算窗口的关闭按钮
- [ ] **预期**: 只关闭概算窗口，主应用继续运行

## 🔍 调试信息

### 查看控制台日志
打开浏览器开发者工具查看日志：

#### 主应用日志 (F12 在主应用窗口)
```
窗口对象已初始化: [Window对象]
初始最大化状态: false
窗口已最小化
窗口已最大化
窗口状态更新: 最大化
```

#### 概算模块日志 (F12 在概算窗口)
```
概算窗口对象已初始化: [Window对象]
概算窗口初始最大化状态: false
概算窗口已最小化
概算窗口已最大化
概算窗口状态更新: 最大化
```

### 常见问题排查

#### 问题1: 按钮点击无反应
**可能原因**: 
- 窗口对象未正确初始化
- Tauri API 未正确加载

**排查方法**:
1. 检查控制台是否有错误信息
2. 确认是否看到"窗口对象已初始化"日志
3. 检查 `@tauri-apps/api` 依赖是否正确安装

#### 问题2: 拖动不生效
**可能原因**:
- 点击了不可拖动的区域
- CSS 样式配置错误

**排查方法**:
1. 确保点击标题栏中间区域（避开按钮）
2. 检查 `data-tauri-drag-region` 属性是否正确设置

#### 问题3: 窗口状态不同步
**可能原因**:
- 窗口状态监听未正确设置

**排查方法**:
1. 检查是否有"窗口状态更新"日志
2. 手动调整窗口大小，观察按钮图标是否变化

## 🎯 预期结果

### 成功标准
- [ ] 主应用所有窗口控制功能正常
- [ ] 概算模块所有窗口控制功能正常
- [ ] 窗口状态实时同步
- [ ] 无控制台错误信息
- [ ] 用户体验流畅

### 性能指标
- 窗口操作响应时间 < 100ms
- 拖动操作流畅无卡顿
- 状态同步延迟 < 50ms

## 📞 问题反馈

如果测试中发现问题，请提供：
1. 具体的操作步骤
2. 预期结果 vs 实际结果
3. 控制台错误信息
4. 操作系统和浏览器版本

## 🎉 测试完成

完成所有测试项目后，你的造价管理系统应该具备：
- ✅ 完整的窗口控制功能
- ✅ 现代化的用户界面
- ✅ 流畅的操作体验
- ✅ 稳定的微前端架构

恭喜！你的应用现在拥有了专业级的桌面应用体验！🚀
