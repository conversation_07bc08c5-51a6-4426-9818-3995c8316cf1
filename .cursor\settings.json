{
  "workbench.colorTheme": "Default Dark+",
  "editor.tabSize": 4,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  },
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  
  // Rust specific settings
  "rust-analyzer.check.command": "clippy",
  "rust-analyzer.check.allTargets": true,
  "rust-analyzer.checkOnSave.command": "clippy",
  "rust-analyzer.cargo.features": "all",
  "rust-analyzer.imports.granularity.group": "module",
  "rust-analyzer.imports.prefix": "by_crate",
  "rust-analyzer.diagnostics.disabled": ["unresolved-proc-macro"],
  "rust-analyzer.completion.addCallArgumentSnippets": true,
  "rust-analyzer.completion.addCallParenthesis": true,
  "rust-analyzer.inlayHints.enable": true,
  "rust-analyzer.inlayHints.chainingHints": true,
  "rust-analyzer.inlayHints.parameterHints": true,
  "rust-analyzer.inlayHints.typeHints": true,
  
  // File associations
  "files.associations": {
    "*.rs": "rust",
    "Cargo.toml": "toml",
    "Cargo.lock": "toml",
    "*.cursorrules": "markdown"
  },
  
  // Search settings
  "search.exclude": {
    "**/target": true,
    "**/Cargo.lock": true,
    "**/.git": true,
    "**/node_modules": true
  },
  
  // Git settings
  "git.autofetch": true,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,
  
  // Terminal settings
  "terminal.integrated.defaultProfile.windows": "PowerShell",
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.defaultProfile.linux": "bash",
  
  // AI settings
  "cursor.chat.model": "gpt-4",
  "cursor.autocomplete.enabled": true,
  "cursor.autocomplete.languages": {
    "rust": true,
    "toml": true,
    "yaml": true,
    "json": true,
    "markdown": true
  },
  
  // ModuForge specific
  "yaml.schemas": {
    "./schemas/node-validation-schema.json": "rules/node_validation_rules.yml",
    "./schemas/plugin-execution-schema.json": "rules/plugin_execution_rules.yml", 
    "./schemas/middleware-chain-schema.json": "rules/middleware_chain_rules.yml"
  }
} 