{"version": "0.2.0", "configurations": [{"type": "lldb", "request": "launch", "name": "Debug ModuForge", "cargo": {"args": ["build", "--bin=moduforge-rs"], "filter": {"name": "moduforge-rs", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "environment": [{"name": "RUST_LOG", "value": "debug"}]}, {"type": "lldb", "request": "launch", "name": "Debug Tests", "cargo": {"args": ["test", "--no-run", "--bin=moduforge-rs"], "filter": {"name": "moduforge-rs", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"type": "lldb", "request": "launch", "name": "Debug Unit Tests", "cargo": {"args": ["test", "--no-run", "--lib"], "filter": {"name": "moduforge-rs", "kind": "lib"}}, "args": [], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"type": "lldb", "request": "launch", "name": "Debug Integration Tests", "cargo": {"args": ["test", "--no-run", "--test"], "filter": {"name": "integration_tests", "kind": "test"}}, "args": [], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}, {"type": "lldb", "request": "launch", "name": "Debug Examples", "cargo": {"args": ["build", "--example=${input:exampleName}"], "filter": {"name": "${input:exampleName}", "kind": "example"}}, "args": [], "cwd": "${workspaceFolder}", "console": "integratedTerminal"}], "inputs": [{"id": "exampleName", "description": "Example to debug", "default": "basic_usage", "type": "promptString"}]}